import React from 'react';
import { Box } from 'zmp-ui';

/**
 * GameHeader component - A consistent header for all game screens
 * 
 * Specifications:
 * - Height: exactly 48px
 * - Background color: #0c1bac (dark blue)
 * - Positioned at the top of each screen
 * - Fixed positioning to stay at top during scroll
 * - z-index above global progress bar
 */
const GameHeader: React.FC = () => {
  return (
    <Box className="game-header">
      {/* Header content can be added here if needed in the future */}
      {/* For now, keeping it empty as per requirements */}
    </Box>
  );
};

export default GameHeader;
