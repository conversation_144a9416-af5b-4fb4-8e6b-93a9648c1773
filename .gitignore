
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Dependency directories
node_modules/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env

# Misc
.DS_Store
Thumbs.db

# Production build
www/

# Unused font files (keeping only variable fonts and essential fallbacks)
src/static/font/Noto_Serif/static/NotoSerif-*Italic*.ttf
src/static/font/Noto_Serif/static/NotoSerif-Black.ttf
src/static/font/Noto_Serif/static/NotoSerif-Extra*.ttf
src/static/font/Noto_Serif/static/NotoSerif-Light.ttf
src/static/font/Noto_Serif/static/NotoSerif-Medium.ttf
src/static/font/Noto_Serif/static/NotoSerif-Semi*.ttf
src/static/font/Noto_Serif/static/NotoSerif-Thin.ttf
src/static/font/Noto_Serif/static/NotoSerif_*Condensed*.ttf
