// Image Upload Screen Styles
.image-upload-screen {
  input[type="file"] {
    display: none;
  }
  .selected-zone {
    max-width: 800px;
  }

  // Header Section with Title and Name Input
  .upload-header-section {
    margin-bottom: 28px;
    .upload-title {
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: normal;
      text-align: center;
      margin-bottom: 12px;
    }

    .name-input-label {
      color: #492405;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      display: block;
      margin-bottom: 4px;
    }

    .name-input-container {
      position: relative;
      text-align: center;

      .dotted-placeholder {
        color: #492405;
        font-size: 16px;
        position: absolute;
        width: 100%;
        text-align: center;
        pointer-events: none;
        transition: opacity 0.3s ease;
      }

      .name-input {
        background: transparent;
        border: none;
        color: #492405;
        font-size: 16px;
        text-align: center;
        width: 100%;
        outline: none;
        padding: 4px 0;
      }
    }
  }

  // Photo Section with Frame and Upload
  .photo-upload-section {
    margin-top: 16px;
    text-align: center;
    position: relative;

    .upload-frame-container {
      cursor: pointer;
      display: inline-block;
      position: relative;

      .upload-frame {
        max-width: 100%;
        height: auto;
      }

      .uploaded-image-container {
        position: absolute;
        top: 50%;
        left: 49%;
        transform: translate(-50%, -49%) rotate(6.5deg) scale(0.85);
        width: 99%;
        height: 79%;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        border-radius: 4px; // Slight rounding to match photo aesthetic

        .uploaded-image {
          // Use contain to ensure the entire image is visible
          object-fit: contain;
          object-position: center;
          
          // For portrait images (taller than wide)
          &.portrait {
            width: auto;
            height: 100%;
            max-width: 100%;
          }
          
          // For landscape images (wider than tall)
          &.landscape {
            width: 100%;
            height: auto;
            max-height: 100%;
          }
          
          // Default before orientation is detected
          &:not(.portrait):not(.landscape) {
            max-width: 100%;
            max-height: 100%;
          }
        }
      }
    }
  }

  // Instructions Section
  .instructions-section {
    margin-top: 0px;

    .instructions-text {
      text-align: center;
      font-size: 20px;
      font-style: italic;
      font-weight: 700;
      line-height: 24px;
      margin: 0;
    }
  }
}