// API Configuration
const API_BASE_URL = 'https://ensure.lifesup.ai/api';

// Common Types and Interfaces
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// API Request Types
export interface ImageUploadRequest {
  user_name: string;
  user_id: string;
  img: File;
}

export interface ImageUploadResponse {
  status: 'success' | 'error';
  message: string;
  user_id: string;
}

export interface VideoGenerationRequest {
  user_id: string;
  score: number;
}

export interface VideoGenerationResponse {
  status: string;
  message: string;
  url: string;
  mp4_url: string;
}

// Mock delay for development/testing
const mockDelay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Image Upload API (Fire-and-forget pattern)
export const uploadImageInBackground = (
  userName: string,
  userId: string,
  imageFile: File
): void => {
  const formData = new FormData();
  formData.append('user_name', userName);
  formData.append('user_id', userId);
  formData.append('img', imageFile);

  // Fire-and-forget pattern - no await, no response handling
  fetch(`${API_BASE_URL}/upload`, {
    method: 'POST',
    body: formData
  }).catch(error => {
    // Silently log any network errors for debugging
    console.error('Background upload error:', error);
  });
};

// Video Generation API
export const generateVideo = async (
  userId: string,
  score: number
): Promise<VideoGenerationResponse> => {
  // Create FormData instead of JSON
  const formData = new FormData();
  formData.append('user_id', userId);
  formData.append('score', score.toString());
  
  const response = await fetch(`${API_BASE_URL}/get-video-sample`, {
    method: 'POST',
    body: formData
  });

  console.log(response);
  
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  const result = await response.json() as VideoGenerationResponse;

  console.log(result);
  
  
  // Validate response structure
  if (!result.status || !result.message || !result.url || !result.mp4_url) {
    throw new Error('Invalid API response structure: missing required fields');
  }
  
  return result;
};

// Error Handling
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Utility Functions for API Health
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await fetch(`${API_BASE_URL}/health`, {
      method: 'GET',
      signal: AbortSignal.timeout(5000) // 5 second timeout
    });
    return response.ok;
  } catch {
    return false;
  }
};

// Network Status
export const isOnline = (): boolean => {
  return navigator.onLine;
};