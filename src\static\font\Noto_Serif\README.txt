Noto Serif Variable Font
========================

This download contains Noto Serif as both variable fonts and static fonts.

Noto Serif is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in these files:
  NotoSerif-VariableFont_wdth,wght.ttf
  NotoSerif-Italic-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Noto Serif:
  static/NotoSerif_ExtraCondensed-Thin.ttf
  static/NotoSerif_ExtraCondensed-ExtraLight.ttf
  static/NotoSerif_ExtraCondensed-Light.ttf
  static/NotoSerif_ExtraCondensed-Regular.ttf
  static/NotoSerif_ExtraCondensed-Medium.ttf
  static/NotoSerif_ExtraCondensed-SemiBold.ttf
  static/NotoSerif_ExtraCondensed-Bold.ttf
  static/NotoSerif_ExtraCondensed-ExtraBold.ttf
  static/NotoSerif_ExtraCondensed-Black.ttf
  static/NotoSerif_Condensed-Thin.ttf
  static/NotoSerif_Condensed-ExtraLight.ttf
  static/NotoSerif_Condensed-Light.ttf
  static/NotoSerif_Condensed-Regular.ttf
  static/NotoSerif_Condensed-Medium.ttf
  static/NotoSerif_Condensed-SemiBold.ttf
  static/NotoSerif_Condensed-Bold.ttf
  static/NotoSerif_Condensed-ExtraBold.ttf
  static/NotoSerif_Condensed-Black.ttf
  static/NotoSerif_SemiCondensed-Thin.ttf
  static/NotoSerif_SemiCondensed-ExtraLight.ttf
  static/NotoSerif_SemiCondensed-Light.ttf
  static/NotoSerif_SemiCondensed-Regular.ttf
  static/NotoSerif_SemiCondensed-Medium.ttf
  static/NotoSerif_SemiCondensed-SemiBold.ttf
  static/NotoSerif_SemiCondensed-Bold.ttf
  static/NotoSerif_SemiCondensed-ExtraBold.ttf
  static/NotoSerif_SemiCondensed-Black.ttf
  static/NotoSerif-Thin.ttf
  static/NotoSerif-ExtraLight.ttf
  static/NotoSerif-Light.ttf
  static/NotoSerif-Regular.ttf
  static/NotoSerif-Medium.ttf
  static/NotoSerif-SemiBold.ttf
  static/NotoSerif-Bold.ttf
  static/NotoSerif-ExtraBold.ttf
  static/NotoSerif-Black.ttf
  static/NotoSerif_ExtraCondensed-ThinItalic.ttf
  static/NotoSerif_ExtraCondensed-ExtraLightItalic.ttf
  static/NotoSerif_ExtraCondensed-LightItalic.ttf
  static/NotoSerif_ExtraCondensed-Italic.ttf
  static/NotoSerif_ExtraCondensed-MediumItalic.ttf
  static/NotoSerif_ExtraCondensed-SemiBoldItalic.ttf
  static/NotoSerif_ExtraCondensed-BoldItalic.ttf
  static/NotoSerif_ExtraCondensed-ExtraBoldItalic.ttf
  static/NotoSerif_ExtraCondensed-BlackItalic.ttf
  static/NotoSerif_Condensed-ThinItalic.ttf
  static/NotoSerif_Condensed-ExtraLightItalic.ttf
  static/NotoSerif_Condensed-LightItalic.ttf
  static/NotoSerif_Condensed-Italic.ttf
  static/NotoSerif_Condensed-MediumItalic.ttf
  static/NotoSerif_Condensed-SemiBoldItalic.ttf
  static/NotoSerif_Condensed-BoldItalic.ttf
  static/NotoSerif_Condensed-ExtraBoldItalic.ttf
  static/NotoSerif_Condensed-BlackItalic.ttf
  static/NotoSerif_SemiCondensed-ThinItalic.ttf
  static/NotoSerif_SemiCondensed-ExtraLightItalic.ttf
  static/NotoSerif_SemiCondensed-LightItalic.ttf
  static/NotoSerif_SemiCondensed-Italic.ttf
  static/NotoSerif_SemiCondensed-MediumItalic.ttf
  static/NotoSerif_SemiCondensed-SemiBoldItalic.ttf
  static/NotoSerif_SemiCondensed-BoldItalic.ttf
  static/NotoSerif_SemiCondensed-ExtraBoldItalic.ttf
  static/NotoSerif_SemiCondensed-BlackItalic.ttf
  static/NotoSerif-ThinItalic.ttf
  static/NotoSerif-ExtraLightItalic.ttf
  static/NotoSerif-LightItalic.ttf
  static/NotoSerif-Italic.ttf
  static/NotoSerif-MediumItalic.ttf
  static/NotoSerif-SemiBoldItalic.ttf
  static/NotoSerif-BoldItalic.ttf
  static/NotoSerif-ExtraBoldItalic.ttf
  static/NotoSerif-BlackItalic.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
