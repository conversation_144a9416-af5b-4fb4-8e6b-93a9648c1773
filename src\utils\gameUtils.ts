// Game Utility Functions
import { v4 as uuidv4 } from 'uuid';

export interface GameSession {
  sessionId: string;
  currentScreen: number;
  uploadedImages: File[];
  surveyResponses: SurveyResponse[];
  startTime: Date;
  completionTime?: Date;
}

export interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
  timestamp: Date;
}

export interface Question {
  id: string;
  type: 'multiple-choice' | 'text' | 'rating' | 'yes-no' | 'checkbox' | 'slider';
  question: string;
  options?: string[];
  required: boolean;
  min?: number;
  max?: number;
}

export interface GameResults {
  score: number;
  category: string;
  insights: string[];
  videoUrl: string;
  shareableImage?: string;
  completionTime: number; // in seconds
}

// Session Management
export const generateSessionId = (): string => {
  return uuidv4();
};

export const saveGameState = (state: GameSession): void => {
  try {
    localStorage.setItem('gameState', JSON.stringify({
      ...state,
      startTime: state.startTime.toISOString(),
      completionTime: state.completionTime?.toISOString()
    }));
  } catch (error) {
    console.error('Error saving game state:', error);
  }
};

export const loadGameState = (): GameSession | null => {
  try {
    const saved = localStorage.getItem('gameState');
    if (!saved) return null;

    const parsed = JSON.parse(saved);
    return {
      ...parsed,
      startTime: new Date(parsed.startTime),
      completionTime: parsed.completionTime ? new Date(parsed.completionTime) : undefined
    };
  } catch (error) {
    console.error('Error loading game state:', error);
    return null;
  }
};

export const clearGameState = (): void => {
  localStorage.removeItem('gameState');
};

// File Validation
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp'
];

export const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
export const MAX_FILES = 5;

export const validateImageFile = (file: File): string | null => {
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
    return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
  }

  if (file.size > MAX_FILE_SIZE) {
    return `File size too large. Maximum size is ${formatFileSize(MAX_FILE_SIZE)}.`;
  }

  return null;
};

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Survey Validation
export const validateSurveyAnswer = (question: Question, answer: string | string[] | number): boolean => {
  if (!question.required && (!answer || (Array.isArray(answer) && answer.length === 0))) {
    return true;
  }

  switch (question.type) {
    case 'text':
      return typeof answer === 'string' && answer.trim().length > 0;

    case 'multiple-choice':
    case 'yes-no':
      return typeof answer === 'string' && answer.length > 0;

    case 'checkbox':
      return Array.isArray(answer) && answer.length > 0;

    case 'rating':
    case 'slider':
      return typeof answer === 'number' &&
        answer >= (question.min || 1) &&
        answer <= (question.max || 10);

    default:
      return false;
  }
};

// Results Calculation
export const calculateGameResults = (
  images: File[],
  responses: SurveyResponse[],
  startTime: Date,
  endTime: Date
): GameResults => {
  // Mock calculation - in real app, this would use AI/ML algorithms
  const baseScore = Math.floor(Math.random() * 50) + 30; // 30-80 base score
  const imageBonus = Math.min(images.length * 5, 20); // Up to 20 bonus points for images
  const completionBonus = responses.length === getSampleQuestions().length ? 10 : 0;

  const totalScore = Math.min(baseScore + imageBonus + completionBonus, 100);

  const categories = [
    'Người con hiếu thảo',
    'Trụ cột gia đình',
    'Tấm lòng vàng',
    'Người bạn đồng hành',
    'Nguồn cảm hứng yêu thương'
  ];

  const category = categories[Math.floor(totalScore / 20)];

  const insights = generateInsights(images, responses, totalScore);

  const completionTime = Math.floor((endTime.getTime() - startTime.getTime()) / 1000);

  return {
    score: totalScore,
    category,
    insights,
    videoUrl: '/static/reward-video.mp4',
    shareableImage: '/static/shareable-result.png',
    completionTime
  };
};

const generateInsights = (
  images: File[],
  responses: SurveyResponse[],
  score: number
): string[] => {
  const allInsights = [
    'Bạn là người luôn biết trân trọng và ghi nhận những nỗ lực của bản thân.',
    'Gia đình là nguồn động lực mạnh mẽ nhất trong cuộc sống của bạn.',
    'Bạn thể hiện tình yêu thương qua những hành động cụ thể, thiết thực.',
    'Bạn hiểu rằng hạnh phúc đích thực đến từ những khoảnh khắc bên người thân.',
    'Bạn có khả năng cân bằng tốt giữa việc chăm sóc bản thân và gia đình.',
    'Bạn là người biết lắng nghe và chia sẻ với những người xung quanh.',
    'Bạn có tầm nhìn tích cực và luôn tin tưởng vào tương lai tươi sáng.',
    'Bạn thể hiện sự trưởng thành qua cách quan tâm đến sức khỏe của ba mẹ.',
    'Bạn hiểu giá trị của thời gian và biết tạo ra những kỷ niệm đẹp.',
    'Bạn là người con biết ơn và luôn tìm cách báo đáp công ơn sinh thành.'
  ];

  // Select insights based on score and responses
  const numInsights = score > 80 ? 4 : score > 60 ? 3 : 2;
  return allInsights.sort(() => 0.5 - Math.random()).slice(0, numInsights);
};

// Survey Questions về chủ đề gia đình và mùa Vu Lan
export const getSampleQuestions = (): Question[] => [
  {
    id: 'q1',
    type: 'multiple-choice',
    question: 'Thời gian qua, bạn đã "vượt" chính mình như thế nào?',
    options: [
      'Biến "chưa biết" thành một chứng chỉ/bằng cấp đàng hoàng nè',
      'Dọn đến nơi ở mới – hoặc biến nhà thành tổ ấm thật sự',
      'Hoàn thành công việc/dự án mà bạn từng nghĩ "chắc mình sẽ không làm được đâu"',
      'Học cách "lắng nghe cơ thể mình" và chăm sóc đúng cách',
      'Vừa chăm được mình, vừa lo chu toàn cho sức khỏe ba mẹ'
    ],
    required: true
  },
  {
    id: 'q2',
    type: 'multiple-choice',
    question: 'Trước những bất định trong cuộc sống, đâu là động lực khiến bạn tiếp tục cố gắng?',
    options: [
      'Nhớ lại lý do mình bắt đầu',
      'Tự nhủ: "Rồi sẽ ổn thôi mà"',
      'Nghỉ ngơi để tiếp thêm năng lượng',
      'Nghĩ đến gia đình, Cha Mẹ – điểm tựa vững chắc cho tôi',
      'Tìm kiếm sự sẻ chia từ bạn bè và những người xung quanh'
    ],
    required: true
  },
  {
    id: 'q3',
    type: 'multiple-choice',
    question: 'Trên hành trình nỗ lực và cố gắng ấy, gia đình với bạn là…?',
    options: [
      'Điểm tựa vững vàng tiếp thêm sức mạnh cho tôi',
      'Cái ô che chở tôi giữa những mưa cuộc đời',
      'Vòng tay rộng mở đón tôi trở về trong mọi hoàn cảnh',
      'Lời động viên truyền động lực',
      'Tấm gương sáng truyền cảm hứng để cho tôi thấy thế nào là sống tử tế và kiên cường'
    ],
    required: true
  },
  {
    id: 'q4',
    type: 'multiple-choice',
    question: 'Thay vì nói "con thương ba mẹ", bạn thường thể hiện sự quan tâm đến Cha Mẹ bằng cách nào?',
    options: [
      'Hỏi thăm sức khỏe thường xuyên, không chỉ "lúc rảnh"',
      'Dẫn ba mẹ đi khám định kỳ, không đợi khi ốm mới đi',
      'Vào bếp tự tay nấu bữa cơm gia đình cho ba mẹ',
      'Lắng nghe và dành thời gian trò chuyện với Cha Mẹ',
      'Nhắc ba mẹ uống sữa mỗi ngày, như cách họ từng chăm tôi khi bé'
    ],
    required: true
  },
  {
    id: 'q5',
    type: 'multiple-choice',
    question: 'Từ mùa Vu Lan này, bạn muốn cùng ba mẹ tạo nên những khoảnh khắc nào?',
    options: [
      'Cùng đi xem bộ phim ba mẹ yêu thích',
      'Cùng dự một đêm nhạc gợi nhớ thanh xuân',
      'Cùng kể chuyện cũ, nghe ba mẹ nói "ngày xưa…"',
      'Cùng nấu một bữa cơm đàng vị nhà',
      'Cùng hát lại bản karaoke "hồi sáu tồng mà vui"',
      'Cùng tập thể dục – mỗi người một động tác, nhưng cùng nhịp',
      'Cùng đi du lịch – có thể gần thôi, nhưng là cùng nhau',
      'Cùng ngồi quán café, nhìn phố phường trôi chầm chậm'
    ],
    required: true
  }
];

// Progress Tracking
export const getProgressPercentage = (currentScreen: number): number => {
  return (currentScreen / 4) * 100;
};

export const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins}:${secs.toString().padStart(2, '0')}`;
};

export const getTimeSpent = (startTime: Date, endTime?: Date): string => {
  const now = endTime || new Date();
  const diffMs = now.getTime() - startTime.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  return formatTime(diffSeconds);
};

// Sharing Functions
export const shareResults = async (results: GameResults): Promise<boolean> => {
  const shareData = {
    title: 'My Game Results',
    text: `I scored ${results.score} points and got "${results.category}"! Check out this amazing game!`,
    url: window.location.href
  };

  if (navigator.share) {
    try {
      await navigator.share(shareData);
      return true;
    } catch (error) {
      console.log('Error sharing:', error);
    }
  }

  // Fallback to clipboard
  try {
    await navigator.clipboard.writeText(shareData.text + ' ' + shareData.url);
    return true;
  } catch (error) {
    console.error('Error copying to clipboard:', error);
    return false;
  }
};

// Image Processing
export const createImagePreview = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e.target?.result) {
        resolve(e.target.result as string);
      } else {
        reject(new Error('Failed to read file'));
      }
    };
    reader.onerror = () => reject(new Error('File reading error'));
    reader.readAsDataURL(file);
  });
};

export const resizeImage = (file: File, maxWidth: number, maxHeight: number, quality: number = 0.8): Promise<File> => {
  return new Promise((resolve) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // Calculate new dimensions
      let { width, height } = img;

      if (width > height) {
        if (width > maxWidth) {
          height = (height * maxWidth) / width;
          width = maxWidth;
        }
      } else {
        if (height > maxHeight) {
          width = (width * maxHeight) / height;
          height = maxHeight;
        }
      }

      canvas.width = width;
      canvas.height = height;

      // Draw and compress
      ctx?.drawImage(img, 0, 0, width, height);

      canvas.toBlob((blob) => {
        if (blob) {
          const resizedFile = new File([blob], file.name, {
            type: file.type,
            lastModified: Date.now()
          });
          resolve(resizedFile);
        } else {
          resolve(file); // Return original if compression fails
        }
      }, file.type, quality);
    };

    img.src = URL.createObjectURL(file);
  });
};
