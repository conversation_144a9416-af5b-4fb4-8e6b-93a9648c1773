<<<<<<< HEAD
# Interactive Game Experience - ZMP App

Ứng dụng game tương tác đa màn hình với tính năng tải lên hình ảnh, khảo sát và video thưởng, được xây dựng trên nền tảng Zalo Mini Program (ZMP).

## 🎮 Tính năng chính

- **4 màn hình game hoàn chỉnh**: <PERSON><PERSON><PERSON> mừng → <PERSON><PERSON><PERSON> ảnh → Khảo sát → Kết quả
- **Tải lên hình ảnh**: Hỗ trợ JPG, PNG, GIF, WebP (tối đa 10MB/file, 5 files)
- **Hệ thống khảo sát**: Nhiều loại câu hỏi (trắc nghiệm, text, rating, slider, checkbox)
- **Video thưởng**: Phát video kết quả cho người dùng
- **Responsive design**: Tối ưu cho mọi kích thước màn hình
- **Lưu trữ local**: Không cần backend, dữ liệu lưu trong localStorage
- **Giao diện tiếng Việt**: Hoàn toàn bằng tiếng Việt

## 🏗️ Cấu trúc dự án

```
src/
├── components/
│   └── game/
│       ├── GameApp.tsx          # Component chính quản lý game
│       ├── WelcomeScreen.tsx    # Màn hình chào mừng
│       ├── ImageUploadScreen.tsx # Màn hình tải ảnh
│       ├── SurveyScreen.tsx     # Màn hình khảo sát
│       └── ResultsScreen.tsx    # Màn hình kết quả
├── utils/
│   ├── gameUtils.ts             # Utilities cho game logic
│   └── api.ts                   # Mock API functions
├── css/
│   └── game.scss               # Styles cho game
└── pages/
    └── index.tsx               # Trang chủ với link vào game
```

## 🚀 Development

### Using Zalo Mini App Extension

1. Install [Visual Studio Code](https://code.visualstudio.com/download) and [Zalo Mini App Extension](https://mini.zalo.me/docs/dev-tools).
1. In the **Home** tab, process **Config App ID** and **Install Dependencies**.
1. Navigate to the **Run** tab, select the suitable launcher, and click **Start**.

### Using Zalo Mini App CLI

1. [Install Node JS](https://nodejs.org/en/download/).
1. [Install Zalo Mini App CLI](https://mini.zalo.me/docs/dev-tools/cli/intro/).
1. **Install dependencies**:
   ```bash
   npm install
   ```
1. **Start** the dev server:
   ```bash
   zmp start
   ```
1. **Open** `localhost:3000` in your browser.

## Deployment

1. **Create** a mini program. For instructions on how to create a mini program, please refer to the [Coffee Shop Tutorial](https://mini.zalo.me/tutorial/coffee-shop/step-1/)

1. **Deploy** your mini program to Zalo using the mini app ID created.

   - **Using Zalo Mini App Extension**: navigate to the **Deploy** panel > **Login** > **Deploy**.
   - **Using Zalo Mini App CLI**:
     ```bash
     zmp login
     zmp deploy
     ```

1. Open the mini app in Zalo by scanning the QR code.

## 🎯 Cách sử dụng

1. **Màn hình chào mừng**: Người dùng xem giới thiệu và bấm "Bắt đầu hành trình"
2. **Tải lên hình ảnh**: Chọn và tải lên tối đa 5 hình ảnh
3. **Trả lời khảo sát**: Hoàn thành 6 câu hỏi với các định dạng khác nhau
4. **Xem kết quả**: Nhận điểm số, danh mục và video thưởng

## 🛠️ Tùy chỉnh

### Thêm câu hỏi khảo sát mới

Chỉnh sửa file `src/utils/gameUtils.ts`, function `getSampleQuestions()`:

```typescript
{
  id: 'q7',
  type: 'multiple-choice',
  question: 'Câu hỏi mới của bạn?',
  options: ['Lựa chọn 1', 'Lựa chọn 2', 'Lựa chọn 3'],
  required: true
}
```

### Thay đổi logic tính điểm

Chỉnh sửa function `calculateGameResults()` trong `src/utils/gameUtils.ts`

### Tùy chỉnh giao diện

Chỉnh sửa file `src/css/game.scss` để thay đổi màu sắc, font chữ, layout

## 📱 Responsive Design

- **Mobile First**: Thiết kế ưu tiên mobile
- **Tablet Support**: Tối ưu cho màn hình tablet
- **Desktop Compatible**: Hoạt động tốt trên desktop

## 🔧 Technical Stack

- **Frontend**: React + TypeScript
- **UI Framework**: ZMP UI (Zalo Mini Program UI)
- **Styling**: SCSS với CSS Variables
- **State Management**: React Hooks + localStorage
- **Build Tool**: Vite
- **No Backend Required**: Tất cả logic chạy client-side

## Resources

- [Zalo Mini App Official Website](https://mini.zalo.me/)
- [ZaUI Documentation](https://mini.zalo.me/documents/zaui/)
- [ZMP SDK Documentation](https://mini.zalo.me/documents/api/)
- [DevTools Documentation](https://mini.zalo.me/docs/dev-tools/)
- [Ready-made Mini App Templates](https://mini.zalo.me/zaui-templates)
- [Community Support](https://mini.zalo.me/community)
=======
# Ensure



## Getting started

To make it easy for you to get started with GitLab, here's a list of recommended next steps.

Already a pro? Just edit this README.md and make it your own. Want to make it easy? [Use the template at the bottom](#editing-this-readme)!

## Add your files

- [ ] [Create](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#create-a-file) or [upload](https://docs.gitlab.com/ee/user/project/repository/web_editor.html#upload-a-file) files
- [ ] [Add files using the command line](https://docs.gitlab.com/ee/gitlab-basics/add-file.html#add-a-file-using-the-command-line) or push an existing Git repository with the following command:

```
cd existing_repo
git remote add origin https://gitlab.lifesup.com.vn/HieuDH/ensure.git
git branch -M main
git push -uf origin main
```

## Integrate with your tools

- [ ] [Set up project integrations](https://gitlab.lifesup.com.vn/HieuDH/ensure/-/settings/integrations)

## Collaborate with your team

- [ ] [Invite team members and collaborators](https://docs.gitlab.com/ee/user/project/members/)
- [ ] [Create a new merge request](https://docs.gitlab.com/ee/user/project/merge_requests/creating_merge_requests.html)
- [ ] [Automatically close issues from merge requests](https://docs.gitlab.com/ee/user/project/issues/managing_issues.html#closing-issues-automatically)
- [ ] [Enable merge request approvals](https://docs.gitlab.com/ee/user/project/merge_requests/approvals/)
- [ ] [Set auto-merge](https://docs.gitlab.com/ee/user/project/merge_requests/merge_when_pipeline_succeeds.html)

## Test and Deploy

Use the built-in continuous integration in GitLab.

- [ ] [Get started with GitLab CI/CD](https://docs.gitlab.com/ee/ci/quick_start/index.html)
- [ ] [Analyze your code for known vulnerabilities with Static Application Security Testing (SAST)](https://docs.gitlab.com/ee/user/application_security/sast/)
- [ ] [Deploy to Kubernetes, Amazon EC2, or Amazon ECS using Auto Deploy](https://docs.gitlab.com/ee/topics/autodevops/requirements.html)
- [ ] [Use pull-based deployments for improved Kubernetes management](https://docs.gitlab.com/ee/user/clusters/agent/)
- [ ] [Set up protected environments](https://docs.gitlab.com/ee/ci/environments/protected_environments.html)

***

# Editing this README

When you're ready to make this README your own, just edit this file and use the handy template below (or feel free to structure it however you want - this is just a starting point!). Thanks to [makeareadme.com](https://www.makeareadme.com/) for this template.

## Suggestions for a good README

Every project is different, so consider which of these sections apply to yours. The sections used in the template are suggestions for most open source projects. Also keep in mind that while a README can be too long and detailed, too long is better than too short. If you think your README is too long, consider utilizing another form of documentation rather than cutting out information.

## Name
Choose a self-explaining name for your project.

## Description
Let people know what your project can do specifically. Provide context and add a link to any reference visitors might be unfamiliar with. A list of Features or a Background subsection can also be added here. If there are alternatives to your project, this is a good place to list differentiating factors.

## Badges
On some READMEs, you may see small images that convey metadata, such as whether or not all the tests are passing for the project. You can use Shields to add some to your README. Many services also have instructions for adding a badge.

## Visuals
Depending on what you are making, it can be a good idea to include screenshots or even a video (you'll frequently see GIFs rather than actual videos). Tools like ttygif can help, but check out Asciinema for a more sophisticated method.

## Installation
Within a particular ecosystem, there may be a common way of installing things, such as using Yarn, NuGet, or Homebrew. However, consider the possibility that whoever is reading your README is a novice and would like more guidance. Listing specific steps helps remove ambiguity and gets people to using your project as quickly as possible. If it only runs in a specific context like a particular programming language version or operating system or has dependencies that have to be installed manually, also add a Requirements subsection.

## Usage
Use examples liberally, and show the expected output if you can. It's helpful to have inline the smallest example of usage that you can demonstrate, while providing links to more sophisticated examples if they are too long to reasonably include in the README.

## Support
Tell people where they can go to for help. It can be any combination of an issue tracker, a chat room, an email address, etc.

## Roadmap
If you have ideas for releases in the future, it is a good idea to list them in the README.

## Contributing
State if you are open to contributions and what your requirements are for accepting them.

For people who want to make changes to your project, it's helpful to have some documentation on how to get started. Perhaps there is a script that they should run or some environment variables that they need to set. Make these steps explicit. These instructions could also be useful to your future self.

You can also document commands to lint the code or run tests. These steps help to ensure high code quality and reduce the likelihood that the changes inadvertently break something. Having instructions for running tests is especially helpful if it requires external setup, such as starting a Selenium server for testing in a browser.

## Authors and acknowledgment
Show your appreciation to those who have contributed to the project.

## License
For open source projects, say how it is licensed.

## Project status
If you have run out of energy or time for your project, put a note at the top of the README saying that development has slowed down or stopped completely. Someone may choose to fork your project or volunteer to step in as a maintainer or owner, allowing your project to keep going. You can also make an explicit request for maintainers.
>>>>>>> 4e7bfaa65bbcae0cff734465afe5260046c7ac6e
