// Noto Serif Font Face Declarations
// Using variable fonts for modern browsers with proper fallback support

// Variable Font - Regular (covers all weights from 100-900)
@font-face {
  font-family: 'Noto Serif';
  src: url('../static/font/Noto_Serif/NotoSerif-VariableFont_wdth,wght.ttf') format('truetype-variations'),
       url('../static/font/Noto_Serif/NotoSerif-VariableFont_wdth,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-stretch: 62.5% 100%;
  font-style: normal;
  font-display: swap;
}

// Variable Font - Italic (covers all italic weights from 100-900)
@font-face {
  font-family: 'Noto Serif';
  src: url('../static/font/Noto_Serif/NotoSerif-Italic-VariableFont_wdth,wght.ttf') format('truetype-variations'),
       url('../static/font/Noto_Serif/NotoSerif-Italic-VariableFont_wdth,wght.ttf') format('truetype');
  font-weight: 100 900;
  font-stretch: 62.5% 100%;
  font-style: italic;
  font-display: swap;
}

// Fallback for browsers that don't support variable fonts
// Only include the most commonly used weight as a fallback
@supports not (font-variation-settings: normal) {
  @font-face {
    font-family: 'Noto Serif';
    src: url('../static/font/Noto_Serif/static/NotoSerif-Regular.ttf') format('truetype');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
  }
  
  @font-face {
    font-family: 'Noto Serif';
    src: url('../static/font/Noto_Serif/static/NotoSerif-Bold.ttf') format('truetype');
    font-weight: 700;
    font-style: normal;
    font-display: swap;
  }
}