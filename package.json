{"name": "ensure", "private": true, "version": "1.0.0", "description": "zmp-blank-templates", "repository": "", "license": "UNLICENSED", "browserslist": ["Android >= 5", "IOS >= 9.3", "Edge >= 15", "Safari >= 9.1", "Chrome >= 49", "Firefox >= 31", "Samsung >= 5"], "scripts": {"login": "zmp login", "start": "zmp start", "dev": "zmp start", "build": "zmp build", "deploy": "zmp deploy", "test": "echo \"No tests specified\" && exit 0"}, "dependencies": {"@lottiefiles/dotlottie-react": "^0.14.4", "@types/uuid": "^10.0.0", "jotai": "^2.12.1", "react": "^18.3.1", "react-dom": "^18.3.1", "uuid": "^11.1.0", "zmp-sdk": "latest", "zmp-ui": "latest"}, "devDependencies": {"@types/react": "^18.3.1", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "cross-env": "^7.0.3", "postcss": "^8.4.38", "postcss-cli": "^8.3.1", "postcss-preset-env": "^6.7.0", "sass": "^1.76.0", "tailwindcss": "^3.4.3", "vite": "^5.2.13", "zmp-vite-plugin": "latest"}}