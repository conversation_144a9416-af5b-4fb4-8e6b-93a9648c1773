import React from "react";
import { getSystemInfo } from "zmp-sdk";
import {
  AnimationRoutes,
  App,
  Route,
  SnackbarProvider,
  ZMPRouter,
  useLocation,
  useNavigate,
} from "zmp-ui";
import { AppProps } from "zmp-ui/app";
import { v4 as uuidv4 } from 'uuid';

import WelcomeScreen from "@/components/game/WelcomeScreen";
import ImageUploadScreen from "@/components/game/ImageUploadScreen";
import SurveyScreen from "@/components/game/SurveyScreen";
import ResultsScreen from "@/components/game/ResultsScreen";
import { initGA4, trackScreenView } from "@/utils/ga4-tracking";

interface GameState {
  sessionId: string;
  currentScreen: number;
  uploadedImages: File[];
  surveyResponses: any[];
  startTime: Date;
  completionTime?: Date;
  userData?: any;
}

const GameRoutes = () => {
  const location = useLocation();
  const navigate = useNavigate();
  
  // Generate a fresh sessionId for each app launch
  const freshSessionId = React.useMemo(() => uuidv4(), []);
  
  // Game state management
  const [gameState, setGameState] = React.useState<GameState>({
    sessionId: freshSessionId,
    currentScreen: 1,
    uploadedImages: [],
    surveyResponses: [],
    startTime: new Date()
  });

  // Load saved state from localStorage on mount, but always use fresh sessionId
  React.useEffect(() => {
    const savedState = localStorage.getItem('gameState');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        setGameState({
          ...parsed,
          // Always use the fresh sessionId, never restore from localStorage
          sessionId: freshSessionId,
          // Don't restore uploadedImages from localStorage as File objects can't be serialized
          // Users will need to re-upload their images if they refresh the page
          uploadedImages: [],
          startTime: new Date(parsed.startTime),
          completionTime: parsed.completionTime ? new Date(parsed.completionTime) : undefined
        });
      } catch (error) {
        console.error('Error loading saved game state:', error);
      }
    }
  }, [freshSessionId]);

  // Save state to localStorage whenever it changes (excluding uploadedImages)
  React.useEffect(() => {
    const stateToSave = {
      ...gameState,
      // Don't save uploadedImages as File objects can't be serialized to JSON
      uploadedImages: []
    };
    localStorage.setItem('gameState', JSON.stringify(stateToSave));
  }, [gameState]);

  // Update current screen based on route and track navigation
  React.useEffect(() => {
    const screenMap: { [key: string]: number } = {
      '/': 1,
      '/upload': 2,
      '/survey': 3,
      '/results': 4
    };

    const screenNameMap: { [key: string]: string } = {
      '/': 'WelcomeScreen',
      '/upload': 'ImageUploadScreen',
      '/survey': 'SurveyScreen',
      '/results': 'ResultsScreen'
    };

    const currentScreen = screenMap[location.pathname] || 1;
    setGameState(prev => ({ ...prev, currentScreen }));
    
    // Track navigation as screen view
    const screenName = screenNameMap[location.pathname] || 'UnknownScreen';
    trackScreenView(screenName, 'Navigation');
  }, [location.pathname]);

  const handleGameStart = () => {
    setGameState(prev => ({
      ...prev,
      startTime: new Date(),
      currentScreen: 2
    }));
  };

  const handleImageUpload = (images: File[]) => {
    setGameState(prev => ({
      ...prev,
      uploadedImages: images
    }));
  };

  const handleSurveyComplete = (responses: any[], userData?: any) => {
    setGameState(prev => ({
      ...prev,
      surveyResponses: responses,
      completionTime: new Date(),
      currentScreen: 4,
      userData
    }));
  };

  const handleGameRestart = () => {
    const newState: GameState = {
      sessionId: uuidv4(),
      currentScreen: 1,
      uploadedImages: [],
      surveyResponses: [],
      startTime: new Date()
    };

    setGameState(newState);
    localStorage.removeItem('gameState');
    navigate('/');
  };

  return (
    <>
      {/* Global Progress Bar */}
      <div className="global-progress">
        <div
          className="progress-fill"
          style={{ width: `${(gameState.currentScreen / 4) * 100}%` }}
        />
      </div>

      <AnimationRoutes>
        <Route
          path="/"
          element={
            <WelcomeScreen onStart={handleGameStart} sessionId={gameState.sessionId} />
          }
        />
        <Route
          path="/upload"
          element={
            <ImageUploadScreen
              onImageUpload={handleImageUpload}
              uploadedImages={gameState.uploadedImages}
            />
          }
        />
        <Route
          path="/survey"
          element={
            <SurveyScreen
              onSurveyComplete={handleSurveyComplete}
              uploadedImages={gameState.uploadedImages}
            />
          }
        />
        <Route
          path="/results"
          element={
            <ResultsScreen
              uploadedImages={gameState.uploadedImages}
              surveyResponses={gameState.surveyResponses}
              userData={gameState.userData}
              onRestart={handleGameRestart}
            />
          }
        />
      </AnimationRoutes>
    </>
  );
};

const Layout = () => {
  React.useEffect(() => {
    // Initialize GA4 tracking when app starts
    initGA4();
  }, []);

  return (
    <App theme={getSystemInfo().zaloTheme as AppProps["theme"]}>
      <SnackbarProvider>
        <ZMPRouter>
          <GameRoutes />
        </ZMPRouter>
      </SnackbarProvider>
    </App>
  );
};
export default Layout;
