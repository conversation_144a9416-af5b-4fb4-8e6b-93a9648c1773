# Header and Footer Implementation Guide

## Summary of Changes

This document outlines the implementation of the header and footer components as requested:

### 1. GameHeader Component (`src/components/game/GameHeader.tsx`)

**Specifications Met:**
- ✅ Height: exactly 48px
- ✅ Background color: #0c1bac (dark blue)
- ✅ Positioned at the top of each screen (fixed positioning)
- ✅ Consistent across all game screens
- ✅ z-index above global progress bar (1001 vs 1000)

**Features:**
- Self-contained component with inline styles for consistency
- Fixed positioning to stay at top during scroll
- Subtle shadow for visual depth
- Ready for future content additions

### 2. Selected Zone as Footer

**Specifications Met:**
- ✅ Positioned at the bottom of the screen
- ✅ Width matches the background image width
- ✅ Functions as a footer component
- ✅ Maintains existing visual styling with select-zone.png background

**Implementation:**
- Moved from main content area to footer position using `marginTop: 'auto'`
- Contains action buttons (like "Chơi" button)
- Maintains background image and overlay styling
- Responsive design with proper padding and alignment

### 3. Layout Restructuring

**Changes Made:**
- **Page Layout**: Added `paddingTop: '48px'` to account for fixed header
- **Global Progress Bar**: Repositioned to `top: '48px'` (below header)
- **Main Content**: New flexible content area between header and footer
- **CSS Updates**: Updated `.zaui-page` and `.selected-zone` styles

## How to Apply Header to Other Screens

### Step 1: Import the GameHeader Component

```typescript
import GameHeader from './GameHeader';
```

### Step 2: Add Header to Page Component

```typescript
return (
  <Page
    className="your-screen-class"
    style={{
      // existing styles...
      paddingTop: '48px' // Add this for fixed header
    }}
  >
    {/* Add this header */}
    <GameHeader />
    
    {/* Your existing content */}
    {/* ... */}
  </Page>
);
```

### Step 3: Update Page Styles (if needed)

Ensure your page has proper spacing for the fixed header:

```typescript
style={{
  // existing background styles...
  paddingTop: '48px', // Account for fixed header
  minHeight: '100vh',
  display: 'flex',
  flexDirection: 'column'
}}
```

## Files Modified

1. **`src/components/game/WelcomeScreen.tsx`**
   - Added GameHeader import and usage
   - Restructured layout with main content area and footer
   - Moved content from selected-zone to main content area
   - Selected-zone now functions as footer with action buttons

2. **`src/components/game/GameHeader.tsx`** (NEW)
   - Reusable header component for all game screens
   - Fixed positioning with proper z-index
   - Dark blue background (#0c1bac) with 48px height

3. **`src/components/game/ImageUploadScreen.tsx`**
   - Added GameHeader as example implementation
   - Shows how to apply header to other screens

4. **`src/css/game.scss`**
   - Updated global progress bar positioning (top: 48px)
   - Modified `.zaui-page` to include header padding
   - Updated `.selected-zone` styles for footer usage

5. **`src/components/layout.tsx`**
   - Updated global progress bar inline style for header positioning

## Testing

- ✅ No TypeScript compilation errors
- ✅ No CSS syntax errors
- ✅ Proper component structure maintained
- ✅ Responsive design considerations included

## Next Steps

1. **Apply to Remaining Screens**: Add GameHeader to `SurveyScreen.tsx` and `ResultsScreen.tsx`
2. **Test Visual Layout**: Run the development server to verify visual appearance
3. **Mobile Testing**: Ensure header and footer work properly on mobile devices
4. **Content Customization**: Add any specific content to the header if needed in the future

## Usage Example

```typescript
// In any game screen component
import GameHeader from './GameHeader';

const YourScreen: React.FC = () => {
  return (
    <Page style={{ paddingTop: '48px' }}>
      <GameHeader />
      {/* Your content */}
    </Page>
  );
};
```

The implementation ensures that:
- Header stays fixed at the top (48px height, #0c1bac background)
- Footer (selected-zone) is positioned at the bottom with proper background
- Main content area is flexible and centered
- Global progress bar is positioned correctly below the header
- All screens can easily adopt the same header pattern
