// CSS Reset / Normalize
// Establishes a clean baseline by removing browser default styles

// Box sizing reset - use border-box for all elements
*,
*::before,
*::after {
  box-sizing: border-box;
}

// Remove default margins and paddings from all elements
* {
  margin: 0;
  padding: 0;
}

// HTML and Body reset
html,
body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
}

// Set consistent line-height and font inheritance
html {
  line-height: 1.15;
  -webkit-text-size-adjust: 100%;
  -ms-text-size-adjust: 100%;
  font-size: 100%;
}

body {
  font-family: var(--font-family);
  line-height: inherit;
  color: inherit;
  background-color: transparent;
}

// Headings reset
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  padding: 0;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
}

// Paragraph and text elements reset
p,
blockquote,
pre,
address {
  margin: 0;
  padding: 0;
}

// List reset
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
  list-style-type: none;
}

li {
  margin: 0;
  padding: 0;
}

// Form elements reset
button,
input,
optgroup,
select,
textarea {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  font-size: inherit;
  line-height: inherit;
  color: inherit;
  background-color: transparent;
  border: none;
  outline: none;
}

// Button specific reset
button {
  cursor: pointer;
  background: none;
  border: none;
  padding: 0;
  font: inherit;
  color: inherit;
  text-align: inherit;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

// Remove default button styling in all browsers
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

// Input and textarea reset
input,
textarea {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: none;
  background: none;
}

// Remove number input spinner buttons
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

// Select reset
select {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  background: none;
  border: none;
}

// Link reset
a {
  color: inherit;
  text-decoration: none;
  background-color: transparent;
}

// Image and media reset
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

// Table reset
table {
  border-collapse: collapse;
  border-spacing: 0;
}

td,
th {
  padding: 0;
}

// Definition list reset
dl,
dd,
dt {
  margin: 0;
  padding: 0;
}

// Fieldset and legend reset
fieldset {
  margin: 0;
  padding: 0;
  border: 0;
  min-width: 0;
}

legend {
  padding: 0;
}

// Summary reset
summary {
  display: list-item;
  cursor: pointer;
}

// Remove default focus outline (should be replaced with custom focus styles)
:focus {
  outline: none;
}

// Ensure hidden attribute works
[hidden] {
  display: none !important;
}

// Text selection
::selection {
  background-color: rgba(0, 0, 0, 0.1);
  color: inherit;
}

::-moz-selection {
  background-color: rgba(0, 0, 0, 0.1);
  color: inherit;
}