// Results Screen Styles
.results-screen {
  &.loading {
    .selected-zone {
      &.loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        text-align: center;
        color: var(--white);
      }
    }
  }
  
  .selected-zone {
    max-width: 800px;
  }

  // Main content specific to results screen
  .main-content.results-main-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100%;
  }

  // Hero section for results
  .hero-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;
  }

  // Video section wrapper
  .video-section.video-wrapper {
    width: 100%;
    padding-top: 20px;
    display: flex;
    justify-content: center;
  }

  // Video iframe container
  .video-iframe {
    width: 100%;
    max-width: 400px;
    aspect-ratio: 9/16;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  }

  // Video placeholder container
  .video-placeholder-container {
    width: 90%;
    max-width: 400px;
    aspect-ratio: 9/16;
    background-color: #f0f0f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #ccc;
  }

  // Video placeholder text
  .video-placeholder-text {
    color: #999;
  }

  // Video container with loading overlay
  .video-container {
    width: 90%;
    max-width: 400px;
    aspect-ratio: 9/16;
    position: relative;
    border-radius: 12px;
    overflow: hidden;
    margin: 0 auto;
  }

  // Video loading overlay
  .video-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1;
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid #DBB664;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }
    
    .loading-text {
      color: #fff;
      font-size: 14px;
      font-weight: 500;
    }
  }

  // Video error container
  .video-error-container {
    width: 90%;
    max-width: 400px;
    aspect-ratio: 9/16;
    background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid #ddd;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -50%;
      width: 200%;
      height: 200%;
      background: radial-gradient(circle, rgba(219, 182, 100, 0.1) 0%, transparent 70%);
      animation: pulse 3s ease-in-out infinite;
    }
    
    .error-content {
      text-align: center;
      padding: 24px;
      position: relative;
      z-index: 1;
      
      .error-icon {
        font-size: 48px;
        margin-bottom: 16px;
        display: block;
      }
      
      .error-title {
        font-size: 18px;
        font-weight: 700;
        line-height: 24px;
        margin-bottom: 12px;
      }
      
      .error-message {
        font-size: 14px;
        color: #666;
        line-height: 20px;
        margin-bottom: 20px;
      }
      
      .retry-button {
        background: linear-gradient(135deg, #DBB664 0%, #E9E197 100%);
        border: none;
        border-radius: 24px;
        padding: 12px 32px;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 2px 8px rgba(219, 182, 100, 0.3);
        
        &:hover:not(:disabled) {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(219, 182, 100, 0.4);
        }
        
        &:active:not(:disabled) {
          transform: translateY(0);
        }
        
        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          background: #ccc;
        }
        
        .retry-text {
          color: #fff;
          font-size: 16px;
          font-weight: 600;
        }
      }
      
      .error-suggestion {
        font-size: 12px;
        color: #888;
        line-height: 18px;
        margin-top: 16px;
        font-style: italic;
      }
      
      .support-info {
        font-size: 14px;
        color: #DBB664;
        font-weight: 600;
        margin: 12px 0;
        padding: 8px 16px;
        background: rgba(219, 182, 100, 0.1);
        border-radius: 8px;
        border: 1px solid rgba(219, 182, 100, 0.3);
      }
      
      .wait-time-info {
        font-size: 13px;
        color: #ff9800;
        font-weight: 500;
        margin: 12px 0;
        font-style: italic;
      }
      
      .error-actions {
        margin-top: 20px;
        text-align: center;
        
        .download-hint-button {
          background: transparent;
          border: 2px solid #DBB664;
          border-radius: 24px;
          padding: 8px 24px;
          margin-top: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          
          &:hover {
            background: rgba(219, 182, 100, 0.1);
            transform: translateY(-2px);
          }
          
          .download-hint-text {
            color: #DBB664;
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
      
      .network-status-indicator {
        position: absolute;
        top: 16px;
        right: 16px;
        
        .offline-badge {
          background: #ff4444;
          color: white;
          padding: 4px 12px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(255, 68, 68, 0.3);
        }
      }
      
      .processing-indicator {
        margin-top: 20px;
        width: 100%;
        
        .processing-bar {
          width: 100%;
          height: 6px;
          background: rgba(219, 182, 100, 0.2);
          border-radius: 3px;
          overflow: hidden;
          margin-bottom: 8px;
        }
        
        .processing-progress {
          height: 100%;
          width: 30%;
          background: linear-gradient(90deg, #DBB664 0%, #E9E197 50%, #DBB664 100%);
          border-radius: 3px;
          animation: processing 2s ease-in-out infinite;
        }
        
        .processing-text {
          font-size: 12px;
          color: #DBB664;
          text-align: center;
          font-style: italic;
        }
      }
    }
  }

  // Text content section
  .text-content {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  // Share heading styles
  .share-heading {
    font-size: 20px;
    font-style: italic;
    font-weight: 700;
    line-height: 20px;
    text-align: center;
  }

  // Share subheading styles
  .share-subheading {
    font-size: 12px;
    font-style: italic;
    font-weight: 700;
    line-height: 16px;
    text-align: center;
  }

  // Prizes section
  .prizes-section {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // Prizes image
  .prizes-image {
    width: 100%;
    max-width: 400px;
    height: auto;
  }



  .action-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    button {
      flex: 1;
      min-height: 48px;
    }
  }

}

// Download Dialog Styles
.download-dialog-modal {
  .zaui-modal-content {
    background: transparent !important;
    padding: 0 !important;
    max-width: 90vw;
    width: 100%;
    max-width: 400px;
  }

  .download-dialog {
    background: radial-gradient(50% 219.26% at 50% 50%, #133985 0%, #023C81 71.63%, #1C2664 100%);
    border-radius: 16px;
    padding: 24px;
    text-align: center;
    position: relative;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
    
    .close-button {
      position: absolute;
      top: 0;
      right: 0;
      transform: translate(50%, -50%);
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: linear-gradient(180deg, #DCAD5D 0%, #DED2A4 25%, #D8BC66 76.44%, #DCAD5D 100%);
      border: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      transition: all 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      
      &:hover {
        transform: translate(50%, -50%) scale(1.1);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      }
      
      &:active {
        transform: translate(50%, -50%) scale(0.95);
      }
      
      svg {
        width: 14px;
        height: 14px;
      }
    }

    .download-dialog-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .download-heading {
      font-size: 24px;
      font-weight: 900;
      font-style: italic;
      line-height: 32px;
      text-align: center;
      margin-bottom: 8px;
    }

    .download-subtitle {
      font-size: 14px;
      font-weight: 700;
      font-style: italic;
      line-height: 100%;
      text-align: center;
      margin-bottom: 16px;
    }

    .instructions-section {
      width: 100%;
      margin: 16px 0;
      
      .instructions-header {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        color: #DBAE5E;
        margin-bottom: 8px;
        text-align: left;
      }

      .instructions-content {
        color: #F4D893;
        border-radius: 8px;
        text-align: left;
      }

      .instructions-text {
        font-size: 14px;
        font-weight: 400;
        line-height: 20px;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;
        
        &:before {
          content: "•";
          position: absolute;
          left: 0;
          color: #DBB664;
          font-size: 18px;
          line-height: 20px;
        }
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .image-button {
      margin-top: 16px;
      position: relative;

      .button-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
text-align: center;
        leading-trim: both;
        text-edge: cap;
        font-size: 20px;
        font-style: normal;
        font-weight: 900;
        line-height: 24px; /* 80% */
        pointer-events: none;
        white-space: nowrap;
        padding-bottom: 10px;
        padding-left: 40px;
      }
    }
  }
}

// Gold gradient text class
.gradient-text-gold {
  background: linear-gradient(87.79deg, #DBB664 1.14%, #E9E197 8.06%, #DBB664 16.34%, #EBD57A 30.5%, #DBB664 38.21%, #E9E197 51.61%, #DBB664 61.5%, #EBD57A 71.2%, #DBB664 85.72%, #D0A556 99.37%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  filter: drop-shadow(0px 0.5px 1px rgba(0, 0, 0, 0.2));
}

// Animations
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes processing {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

// Responsive styles
@media (max-width: 768px) {
  .results-screen {
    .video-error-container {
      width: 85%;
      max-width: 350px;
      
      .error-content {
        padding: 20px;
        
        .error-icon {
          font-size: 40px;
        }
        
        .error-title {
          font-size: 16px;
        }
        
        .error-message {
          font-size: 13px;
        }
      }
    }
    
    .video-container {
      width: 85%;
      max-width: 350px;
      margin: 0 auto;
    }
  }
  
  .download-dialog-modal {
    .download-dialog {
      .close-button {
        width: 28px;
        height: 28px;
        
        svg {
          width: 12px;
          height: 12px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .results-screen {
    .video-error-container {
      width: 80%;
      max-width: 300px;
      
      .error-content {
        padding: 16px;
        
        .error-icon {
          font-size: 36px;
          margin-bottom: 12px;
        }
        
        .error-title {
          font-size: 15px;
          line-height: 20px;
        }
        
        .error-message {
          font-size: 12px;
          line-height: 18px;
        }
        
        .retry-button {
          padding: 10px 24px;
          
          .retry-text {
            font-size: 14px;
          }
        }
        
        .error-suggestion {
          font-size: 11px;
          line-height: 16px;
        }
      }
    }
    
    .video-container {
      width: 80%;
      max-width: 300px;
      margin: 0 auto;
      
      .video-loading-overlay {
        .loading-spinner {
          width: 40px;
          height: 40px;
          border-width: 3px;
        }
        
        .loading-text {
          font-size: 13px;
        }
      }
    }
  }
  
  .download-dialog-modal {
    .download-dialog {
      padding: 20px;
      
      .close-button {
        width: 24px;
        height: 24px;
        
        svg {
          width: 10px;
          height: 10px;
        }
      }
    }
  }
}
